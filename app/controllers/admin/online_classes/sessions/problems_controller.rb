module Admin
  module OnlineClasses
    module Sessions
      class ProblemsController < Admin::AdminController
        include Admin::Exercises<PERSON><PERSON><PERSON>

        def index
          load_topics_and_chapters
          @session = OnlineClassSession.find(params[:session])
          @index = params[:index].to_i
          @groups = @session.homework_groups
          @tasks = @session.homework_tasks
          sections = process_tasks(@session)
          @custom_sections = sections[:custom_sections]
          @prereading_sections = sections[:prereading_sections]
          @chapter_name = sections[:chapter_names]
        end

        def update_homework_status
          @homework_session = OnlineClassSession.find(params[:session])

          if @homework_session.homework_task_data.blank?
            flash[:error] = "Please add Problems to Homework"
          else
            @homework_session.update(homework_status: params[:status])
          end
          render js: "window.location='#{admin_homework_problems_path(index: params[:index], session: @homework_session)}'"
        end

        def homework_task_type
          online_class_session = OnlineClassSession.find(params[:online_class_session_id])
          chapter = Chapter.find_by(id: params[:chapter_id])
          homework_task_data = online_class_session.homework_task_data
          task = HomeworkTask.find_by(id: homework_task_data, task_type: params[:type]) if homework_task_data.present?

          if chapter.present?
            case params[:type]
            when "custom_test"
              problem_ids = task&.data || []
              @problems = chapter.problems unless chapter.nil?
              @problems = @problems.where("content ILIKE ?", "%#{params[:search_key_param]}%") if params[:search_key_param].present?
            when "pre_reading"
              topic_ids = task&.data || []
              @topics = chapter.topics unless chapter.nil?
              @topics = @topics.where("name ILIKE ?", "%#{params[:search_key_param]}%") if !chapter.nil? && params[:search_key_param].present?
            end
          end

          respond_to do |format|
            format.js
          end
        end

        def chapter_test
          online_class_session = OnlineClassSession.find(params[:online_class_session_id])
          online_class_session.homework_task_data
          @chapter = Chapter.find_by(id: params[:chapter_id])
          @chapter_test_level = Exercise.where(type: "Problem", chapter_id: @chapter.id).distinct.pluck(:level)
          @chapter_test_level = @chapter_test_level.search(params[:search_key_param], false) if params[:search_key_param].present?

          respond_to do |format|
            format.js
          end
        end

        def chapters
          @chapters = Chapter.select(:id, :name).in_section(section_param).ordered

          respond_to do |format|
            format.js
          end
        end

        def add_problems
          @online_class_session = OnlineClassSession.find(params[:online_class_session_id])
          problem_ids = params[:problem_ids].split(",").map(&:to_i)
          task_type = params[:task_type]
          @mode = params[:mode]

          task = @online_class_session.homework_tasks.find_or_initialize_by(task_type: task_type)
          task.data ||= []
          if @mode == "add"
            task.data += problem_ids
            task.data = task.data.flatten.uniq
          else
            task.data = problem_ids.uniq
          end

          task.save!

          unless @online_class_session.homework_task_data.include?(task.id)
            @online_class_session.homework_task_data << task.id
            @online_class_session.save!
          end

          respond_to do |format|
            format.js do
              @tasks = @online_class_session.homework_tasks
              sections = process_tasks(@online_class_session)
              @custom_sections = sections[:custom_sections]
              @prereading_sections = sections[:prereading_sections]
              @chapter_name = sections[:chapter_names]
            end
          end
        end

        def add_group
          @online_class_session = OnlineClassSession.find(params[:online_class_session_id])
          group_name = params[:name].present? ? params[:name] : "Session #{@online_class_session.number_of_session} - Tasks"
          @group = @online_class_session.homework_groups.new(name: group_name)

          if @group.save
            # Set up variables needed for the response
            @session = @online_class_session
            @groups = @session.homework_groups
            @tasks = @session.homework_tasks
            sections = process_tasks(@session)
            @custom_sections = sections[:custom_sections]
            @prereading_sections = sections[:prereading_sections]
            @chapter_name = sections[:chapter_names]

            respond_to do |format|
              format.js # This will render add_group.js.erb
            end
          else
            respond_to do |format|
              format.js { render json: { error: "Failed to create group" }, status: :unprocessable_entity }
            end
          end
        end

        def edit_homework_task
          task_data = params[:task_data]

          if task_data.present?
            case params[:type]
            when "custom_test"
              @problems = Problem.where(id: task_data.map(&:to_i))
            when "pre_reading"
              @topics = Topic.where(id: task_data.map(&:to_i))
            end
          end

          respond_to do |format|
            format.js { render "homework_task_type" }
          end
        end

        def destroy
          @session = OnlineClassSession.find_by_id(params[:session].to_i)
          @task = @session.homework_tasks.find(params[:id])
          @task.destroy

          @session.homework_task_data = @session.homework_task_data - [@task.id]
          @session.save
          @remaining_task_count = @session.homework_tasks.count

          respond_to do |format|
            format.js
          end
        end

        private

        def section_param
          params&.dig(:section)
        end

        def process_tasks(session)
          tasks = session.homework_tasks
          custom_test_ids = tasks.select { |t| t.task_type == "custom_test" }.flat_map(&:data)
          prereading_ids = tasks.select { |t| t.task_type == "pre_reading" }.flat_map(&:data)
          chapter_ids = tasks.select { |t| t.task_type == "chapter_test" }.flat_map(&:data)

          @custom_problems = Problem.includes(:chapter).where(id: custom_test_ids)
          @prereading_topics = Topic.where(id: prereading_ids)
          @chapter_tests = chapter_ids.map(&:to_i)

          custom_sections = @custom_problems.map(&:section).compact.map(&:capitalize).uniq
          prereading_sections = @prereading_topics.map(&:section).compact.map(&:capitalize).uniq
          chapter_names = Chapter.where(id: @chapter_tests).pluck(:name)

          {
            custom_sections: "(#{custom_sections.join(', ')})",
            prereading_sections: "(#{prereading_sections.join(', ')})",
            chapter_names: chapter_names,
            tasks: tasks
          }
        end
      end
    end
  end
end
