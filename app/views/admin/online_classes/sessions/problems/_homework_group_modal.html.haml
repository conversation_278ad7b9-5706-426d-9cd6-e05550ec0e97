
.modal.fade.task-modal#add-homework-group-modal
  .modal-dialog.modal-lg.modal-dialog-scrollable.modal-dialog-centered
    .modal-content
      .modal-header
        %h5.modal-title= t("admin.actions.online_class.home_work.homework_group.title")
        %button.fas.fa-times.btn-close.close-modal-button.opacity-100{ "data-bs-dismiss": "modal", "aria-label": "Close", type: "button" }
      .modal-body
        %h6.fs-13.fw-semibold.group-name= t("admin.actions.online_class.home_work.homework_group.group_name")
        = form_for HomeworkGroup.new, url: add_group_admin_homework_problems_path(online_class_session_id: @session.id), method: :post, remote: true, class: "homework-group-form show-overlay" do |f|
          = f.text_field :name, class: "form-control fs-16 group-name-text-field", placeholder: t("admin.actions.online_class.home_work.homework_group.enter_group_name")
          .mt-3.d-flex.justify-content-end.homework-group-btn
            %button.btn.btn-outline-secondary.close-modal-button.fs-14{ data: { bs_dismiss: "modal" } } CANCEL
            = button_tag t("admin.actions.online_class.home_work.homework_group.create_group"), class: "btn btn-primary btn-save-group fs-14", type: "submit"
            -# %button.btn.btn-primary.btn-save-group.fs-14.show-overlay{ type: "submit" }= t("admin.actions.online_class.home_work.homework_group.create_group")