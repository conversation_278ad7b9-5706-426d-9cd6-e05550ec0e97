require "spec_helper"

RSpec.feature "User visits Score Chart page" do
  describe "on GMAT" do
    it "renders properly" do
      visit gmat_focus_score_chart_and_calculator_path

      expect(page).to have_content "Score Chart"
      expect(page).to have_current_path(gmat_focus_score_chart_and_calculator_path)
      expect(page).to have_content("Your GMAT Section Scores")
      expect(page).to have_content("Scores go from 60 to 90 in increments of 1.")
      expect(page).to have_content("Want to take your prep to the next level?")
      expect(page).to have_content("TOTAL SCORE")
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit gmat_focus_score_chart_and_calculator_path
      expect(page).to have_content("not found")
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "shows 'Not found Page'" do
      visit gmat_focus_score_chart_and_calculator_path
      expect(page).to have_content("not found")
    end
  end
end
