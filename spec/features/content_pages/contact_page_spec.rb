require "spec_helper"

RSpec.feature "User visits contact page", js: true do
  it "is accesible from homepage" do
    visit_homepage

    within("footer") do
      click_link "Contact"
    end

    expect(page).to have_content "Contact Us"
    expect(page).to have_current_path(contact_path)
    expect(page).to have_selector("header#guest-navbar", count: 1)
    expect(page).to have_selector("footer#guest-footer", count: 1)
  end

  context "when accesing directly" do
    before :each do
      visit contact_path
    end

    it "renders properly" do
      expect(page).to have_content "Contact Us"
      expect(page).to have_current_path(contact_path)
    end

    context "when submitting empty form" do
      it "shows error messages" do
        click_on "Send Message"

        expect(page).to have_content "Your message can't be sent yet. Please read the details under each field"
        expect(page).to have_content "Please enter your Name"
        expect(page).to have_content "Please enter your Last Name"
        expect(page).to have_content "Please enter a valid Email Address"
        expect(page).to have_content "Please enter the Message you want to send us"
      end
    end

    context "when submitting properly filled form" do
      it "shows a thank you message" do
        fill_in "contact_form[name]",     with: "<PERSON>"
        fill_in "contact_form[lastname]", with: "<PERSON><PERSON>"
        fill_in "contact_form[email]",    with: "<EMAIL>"
        fill_in "contact_form[message]",  with: "messsage"

        click_on "Send Message"

        expect(page).to have_content "Your message was sent successfully! We’ll keep in touch soon"
      end
    end
  end
end
