require "spec_helper"

RSpec.feature "User visits Berkeley page", js: true do
  describe "on GMAT" do
    it "renders gmat berkeley page" do
      visit berkeley_path

      expect(page).to have_content(/ONLINE SELF-STUDY COURSE/)
      expect(page).to have_content(/ONLINE LIVE CLASSES/)
      expect(page).to have_content(/PRIVATE ONLINE TUTORING/)
      expect(page).to have_content(/Learn at your own pace with our 5-star rated GMAT self-study course./)
    end

    context "when click start studying" do
      it "redirects to plans page" do
        visit berkeley_path

        expect(page).to have_link "START STUDYING"

        first(".start-studying").click

        expect(page).to have_content(/5-day trial/)
        expect(page).to have_content(/Get started on the road to test-day success with one of our GMAT study plans./)
      end
    end

    context "when click see available classes" do
      it "redirects to online class page" do
        visit berkeley_path

        expect(page).to have_link "SEE AVAILABLE CLASSES"

        find(".see-classes").click

        expect(page).to have_content(/A powerful GMAT course taught live online/)
      end
    end

    context "when click book a free consultation" do
      it "redirects to free consultation page" do
        visit berkeley_path

        expect(page).to have_link "BOOK A FREE CONSULTATION"

        find(".book-consultation .book").click

        expect(page).to have_content(/First Name/)
      end
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders gre berkeley page" do
      visit berkeley_path

      expect(page).to have_content(/ONLINE SELF-STUDY COURSE/)
      expect(page).to have_content(/ONLINE LIVE CLASSES/)
      expect(page).to have_content(/PRIVATE ONLINE TUTORING/)
      expect(page).to have_content(/Learn at your own pace with our 5-star rated GRE self-study course./)
      expect(page).to have_content(/live GRE classes./)
      expect(page).to have_content(/Earn Your Best GRE Score/)
    end

    context "when click start studying" do
      it "redirects to plans page" do
        visit berkeley_path

        expect(page).to have_link "START STUDYING"

        first(".start-studying").click

        expect(page).to have_content(/5-day trial/)
        expect(page).to have_content(/Target Test Prep GRE/)
      end
    end

    context "when click see available classes" do
      it "redirects to online class page" do
        visit berkeley_path

        expect(page).to have_link "SEE AVAILABLE CLASSES"

        find(".see-classes").click

        expect(page).to have_content(/LiveTeach/)
        expect(page).to have_content("GRE course taught live online")
      end
    end

    context "when click book a free consultation" do
      it "redirects to free consultation page" do
        visit berkeley_path

        expect(page).to have_link "BOOK A FREE CONSULTATION"

        find(".book-consultation .book").click

        expect(page).to have_content(/First Name/)
      end
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "renders gre berkeley page" do
      visit berkeley_path

      expect(page).to have_content(/ONLINE SELF-STUDY COURSE/)
      expect(page).to have_content(/PRIVATE ONLINE TUTORING/)
      expect(page).to have_content(/Learn at your own pace with our 5-star rated EA self-study course./)
      expect(page).to have_content(/Earn Your Best EA Score/)
    end

    context "when click start studying" do
      it "redirects to plans page" do
        visit berkeley_path

        expect(page).to have_link "START STUDYING"

        first(".start-studying").click

        expect(page).to have_content(/5-day trial/)
        expect(page).to have_content(/Target Test Prep EA/)
      end
    end

    context "when click book a free consultation" do
      it "redirects to free consultation page" do
        visit berkeley_path

        expect(page).to have_link "BOOK A FREE CONSULTATION"

        find(".book-consultation .book").click

        expect(page).to have_content(/First Name/)
      end
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "renders gre berkeley page" do
      visit berkeley_path

      expect(page).to have_content(/ONLINE SELF-STUDY COURSE/)
      expect(page).to have_content(/PRIVATE ONLINE TUTORING/)
      expect(page).to have_content(/Learn at your own pace with our 5-star rated SAT self-study course./)
      expect(page).to have_content(/Earn Your Best SAT Score/)
    end

    context "when click start studying" do
      it "redirects to plans page" do
        visit berkeley_path

        expect(page).to have_link "START STUDYING"

        first(".start-studying").click

        expect(page).to have_content(/5-day trial/)
        expect(page).to have_content(/Target Test Prep SAT/)
      end
    end

    context "when click book a free consultation" do
      it "redirects to free consultation page" do
        visit berkeley_path

        expect(page).to have_link "BOOK A FREE CONSULTATION"

        find(".book-consultation .book").click

        expect(page).to have_content(/First Name/)
      end
    end
  end
end
