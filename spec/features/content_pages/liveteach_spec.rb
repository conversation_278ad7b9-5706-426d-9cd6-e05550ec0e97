require "spec_helper"

RSpec.feature "User visits liveteach page" do
  it "should render properly" do
    visit liveteach_path

    expect(page).to have_content "Turn any space into a live GMAT classroom"
  end

  it "accesses from homepage" do
    visit_homepage

    within("footer") do
      click_link "Live Classes"
    end

    expect(page).to have_content(/Turn any space into a live GMAT classroom/i)
    expect(page).to have_current_path(liveteach_path)
  end
end
