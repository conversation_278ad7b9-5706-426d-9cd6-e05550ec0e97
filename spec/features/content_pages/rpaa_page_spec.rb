require "spec_helper"

RSpec.feature "User visits RPAA page" do
  describe "on GMAT" do
    it "renders properly" do
      visit rpaa_path

      expect(page).to have_content "Riordan-focused"
      expect(page).to have_current_path(rpaa_path)
    end

    context "when click on enroll now" do
      it "redirects to subscribe page for annual RPAA page" do
        visit rpaa_path

        expect(page).to have_selector("section a.btn-primary-app", count: 5)

        first(".hero").find(:link, "enroll now").click

        expect(page).to have_content "1-year access RPAA"
      end
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders properly" do
      visit rpaa_path

      expect(page).to have_content "Riordan-focused"
      expect(page).to have_selector(".partner-introduction")
      expect(page).to have_current_path(rpaa_path)
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit rpaa_path
      expect(page).to have_content("not found")
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "shows 'Not found Page'" do
      visit rpaa_path
      expect(page).to have_content("not found")
    end
  end
end
