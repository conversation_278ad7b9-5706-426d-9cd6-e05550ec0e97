require "spec_helper"

RSpec.feature "User visits custom static page" do
  context "when translation with static page exists" do
    before do
      I18n.backend.store_translations(:en, { "user.static_pages.test_static_page" => "Test Static Page" }, escape: false)
    end

    it "renders properly" do
      visit "/test_static_page"

      expect(page).to have_content "Test Static Page"
      expect(page).to have_current_path("/test_static_page")
    end
  end

  context "when translations does not exist" do
    it "renders 404" do
      visit "/test_static_page_missing"

      expect(page).to have_current_path("/test_static_page_missing")
      expect(page).to have_content "Page not found"
      expect(page).to_not have_content "Test Static Page"
    end
  end
end
