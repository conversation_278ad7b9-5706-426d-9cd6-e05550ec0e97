require "spec_helper"

RSpec.feature "User visits Marketplane page" do
  describe "on GMAT" do
    let(:default_sale) { create(:sale_banner, start_date: Date.parse("2022-12-07"), end_date: Date.parse("2022-12-13"), sale_type: "default") }

    before do
      allow(SaleBanner).to receive(:active_sale).and_return(default_sale)
      visit marketplace_plans_path
    end

    it "renders properly" do
      expect(page).to have_content "Marketplace Listeners"

      expect(page).to have_content "TTP is a globally-recognized GMAT course"
      expect(page).to have_content "Frequently asked questions"
      expect(page).to have_css("script#recurly-lib", visible: false)
    end

    it "has 9 sections" do
      page.assert_selector("section", count: 9)
    end

    it "has an advertising section" do
      page.assert_selector("section.advertising")
      expect(page).to have_content "Need to take your GMAT score to new heights?"
    end

    it "opens subscription modal from 'start now' link" do
      within ".trial-section" do
        click_link "TRY FOR FREE"
      end

      expect(page).to have_content "Trial Access - Plan Purchase"
    end

    it "opens subscription modal from 'Try us for FREE' link" do
      within "section.advertising" do
        click_link "TRY FOR FREE"
      end

      expect(page).to have_content "Personal Details"
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit marketplace_plans_path
      expect(page).to have_content("not found")
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "shows 'Not found Page'" do
      visit marketplace_plans_path
      expect(page).to have_content("not found")
    end
  end
end
