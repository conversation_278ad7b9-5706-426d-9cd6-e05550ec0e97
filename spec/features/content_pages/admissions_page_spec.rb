require "spec_helper"

RSpec.feature "User visits Admissions page", js: true do
  describe "on GMAT" do
    before :each do
      visit admissions_path
    end

    it "renders admission page" do
      expect(page).to have_content(/Elite MBA Admissions Consulting/)
      expect(page).to have_content(/Packages & Services/)
      expect(page).to have_content(/Why TTP/)
      expect(page).to have_content(/Our Consultants/)
      expect(page).to have_selector(".user-btn")
      expect(page).to have_selector(".request-consultation-btn")
      expect(page).to have_content(/Our MBA Admissions Consulting Services in Detail/)
      expect(page).to have_content(/Your Ultimate MBA Admissions Resource/)
      expect(page).to have_content(/Industry-recognized MBA consultants at your service/)
      expect(page).to have_content(/TTP Can Help You Wherever You Are In Your MBA Journey/)
      expect(page).to have_content(/Frequently asked questions/)
    end

    context "when click on free consultation button" do
      it "opens free consultation modal" do
        first(".request-consultation-btn").click

        expect(page).to have_content(/Request a Free Consultation and be One Step Closer to your Dream MBA School!/)
        expect(page).to have_content(/NAME/)
        expect(page).to have_content(/EMAIL/)
        expect(page).to have_content(/PHONE NUMBER - OPTIONAL/)
        # expect(page).to have_content(/PREFERRED CONSULTANT/)
        expect(page).to have_content(/WHAT TYPE OF PROGRAM WOULD YOU LIKE TO APPLY TO?/)
        expect(page).to have_content(/GOAL MATRICULATION YEAR/)
        expect(page).to have_content(/NEXT/)
      end
    end

    context "when click on show all service button" do
      it "shows all the servcics list items" do
        button = find(".see-all-services-btn")

        expect(button).to have_text("See All Services")

        button.click
        expect(button).to have_text("Hide Services")

        expect(page).to have_content(/Interview Prep/)
        expect(page).to have_content(/Interviewing at a business school is very different from a job interview/)
      end
    end

    context "when click on explore test prep bundles" do
      xit "navigates to the test prep bundles section" do
        find(".explore-test-prep-btn").click

        expect(page).to have_content(/Test prep bundles/)
        expect(page).to have_content(/For applicants looking for specialized support on /)
        expect(page).to have_css(".bundle-card")

        expect(page).to have_content(/TTP Self-Study/)
        expect(page).to have_content(/MBA Admissions/)
        expect(page).to have_content(/BUNDLE & SAVE/)
        expect(page).to have_content(/Learn about TTP Self-Study/)
      end
    end

    context "when click on test prep bundles learn about btn" do
      xit "navigates to the specific redirect path page" do
        link = find(".bundle-a .learn_about a")
        expect(URI(link[:href]).path).to eq(root_path)
        expect(link[:target]).to eq("_blank")
      end
    end

    context "when user fill the free consultation modal" do
      let(:user) { FactoryBot.create(:member) }

      it "fill admissions free consultation form" do
        first(".request-consultation-btn").click

        fill_in "name", with: user.first_name
        fill_in "last_name", with: user.last_name
        fill_in "email", with: user.email
        find("#country").find(:xpath, "option[2]").select_option
        find("#type_of_program").find(:xpath, "option[2]").select_option
        find("#plan_to_start").find(:xpath, "option[2]").select_option

        find(".next-btn").click

        attach_file("resume", "#{Rails.root}/spec/support/sample.pdf", visible: false)
        fill_in "linkedin_url", with: "TestLinkedinprofileurl"
        fill_in "ug_gpa", with: "20"
        find("#gmat_score").find(:xpath, "option[2]").select_option
        find("#gre_score").find(:xpath, "option[2]").select_option
        find("#ea_score").find(:xpath, "option[2]").select_option
        fill_in "current_industry", with: "Test"
        find("#you_learn_about_us").find(:xpath, "option[2]").select_option
        choose "are_you_intersted_yes"

        find(".submit-btn").click

        expect(page).to have_content(/Your free consultation request was received successfully!/)
      end
    end

    context "when user select any services and redirect to to cart page" do
      let(:user) { FactoryBot.create(:member) }

      it "renders properly" do
        first(".service .container .card .inner-card a").click

        expect(page).to have_content("Hourly Consulting")
        expect(page).to have_content(/HOURS/)
        expect(page).to have_content(/Review Your Order/)
        expect(page).to have_css(".item-hours")
        expect(page).to have_current_path(cart_path)
      end
    end

    context "when user select any plan from comprehensive school package fees and redirect to to cart page" do
      let(:user) { FactoryBot.create(:member) }

      it "renders properly" do
        first("#packages-and-service .container .card .card-body table tbody tr .text-right").click

        expect(page).to have_content("One-School Package")
        expect(page).to have_content(/Review Your Order/)
        expect(page).not_to have_content("HOURS")
        expect(page).not_to have_css(".item-hours")
        expect(page).to have_current_path(cart_path)
      end
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
      visit admissions_path
    end

    context "when clicking on Explore Test Prep Bundles" do
      xit "navigates to the test prep bundles section" do
        find(".explore-test-prep-btn").click

        expect(page).to have_content(/Test prep bundles/)
        expect(page).to have_content(/For applicants looking for specialized support on /)
        expect(page).to have_css(".bundle-card")
        expect(page).to have_content(/TTP Self-Study/)
        expect(page).to have_content(/MBA Admissions/)
        expect(page).to have_content(/BUNDLE & SAVE/)
        expect(page).to have_content(/Learn about TTP Self-Study/)
      end
    end

    context "when viewing the test prep bundles section" do
      xit "renders the expected bundles and excludes OnDemand + MBA Admissions bundle" do
        %w(bundle-a bundle-c bundle-d).each do |bundle_class|
          expect(page).to have_css(".bundle-card.#{bundle_class}")
        end

        expect(page).not_to have_css(".bundle-card.bundle-b")
      end
    end

    context "when clicking Learn about on a test prep bundle" do
      xit "navigates to the root path from bundle a" do
        link = find(".bundle-a .learn_about a")

        expect(URI(link[:href]).path).to eq(root_path)
        expect(link[:target]).to eq("_blank")
      end

      xit "navigates to the Live Online Classes path from bundle c" do
        link = find(".bundle-c .learn_about a")

        expect(URI(link[:href]).path).to eq(liveteach_path)
        expect(link[:target]).to eq("_blank")
      end
    end
  end
end
