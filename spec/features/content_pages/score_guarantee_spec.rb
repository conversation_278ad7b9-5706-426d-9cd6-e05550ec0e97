require "spec_helper"

RSpec.feature "User visits score guarantee page" do
  let(:default_sale) { create(:sale_banner, start_date: Date.parse("2022-12-07"), end_date: Date.parse("2022-12-13"), sale_type: "default") }

  it "should render properly" do
    visit score_guarantee_path

    expect(page).to have_content "Score Improvement Guarantee"
  end

  it "accesses from homepage" do
    visit_homepage

    within("footer") do
      click_link "Score Guarantee"
    end

    expect(page).to have_content(/Score Improvement Guarantee/i)
    expect(page).to have_content("We’re so confident you’ll have success with our course, we guarantee it.", wait: 5)
    expect(page).to have_current_path(score_guarantee_path)
  end
end
