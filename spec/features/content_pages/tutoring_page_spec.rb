require "spec_helper"

RSpec.feature "User visits tutoring page" do
  it "accesses from homepage" do
    visit_homepage

    within("footer") do
      click_link "Tutoring"
    end

    expect(page).to have_content(/PRIVATE ONLINE GMAT TUTORING/i)
    expect(page).to have_current_path(tutoring_path)
  end
end

RSpec.feature "Student visits tutoring page", js: true do
  let!(:user) { FactoryBot.create(:member) }
  let!(:study_plan) { FactoryBot.create(:study_plan, :complete, track: user.track) }

  it "accesses from navbar" do
    login(user)

    visit root_path

    within "nav.navbar" do
      click_link "Tutoring"
    end

    expect(page).to have_content(/PRIVATE GMAT TUTORING ONLINE FOR GMAT/i)
    expect(page).to have_current_path(online_tutoring_path)
  end
end
