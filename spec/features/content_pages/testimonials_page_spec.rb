require "spec_helper"

RSpec.feature "User visits testimonials page" do
  it "is reachable from homepage" do
    visit_homepage

    within("footer") do
      click_link "Testimonials"
    end

    expect(page).to have_content "Testimonials"
    expect(page).to have_current_path(testimonials_path)
  end

  it "renders properly" do
    visit testimonials_path

    expect(page).to have_content "Testimonials"
    expect(page).to have_current_path(testimonials_path)
  end
end
