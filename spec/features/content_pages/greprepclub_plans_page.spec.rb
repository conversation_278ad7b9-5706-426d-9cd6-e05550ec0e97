require "spec_helper"

RSpec.feature "User visits GREPrep Club page" do
  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
      visit greprepclub_plans_path
    end

    it "renders properly" do
      expect(page).to have_content "GRE Prep Club Members"

      # shared partials required
      expect(page).to have_content "We help students achieve great scores"
      expect(page).to have_content "Frequently asked questions"
      expect(page).to have_css("script#recurly-lib", visible: false)
    end

    it "has 9 sections" do
      page.assert_selector("section", count: 8)
    end

    it "has an advertising section" do
      page.assert_selector("section.advertising")
      expect(page).to have_content "Try us for 5 days"
    end

    it "opens subscription modal from 'start now' link" do
      within ".trial-section" do
        click_link "Try us for $1"
      end

      expect(page).to have_content(/Trial Access Plan/i)
    end

    it "opens subscription modal from 'Try us for $1' link" do
      within "section.advertising" do
        click_link "Try our course for 1$"
      end

      expect(page).to have_content(/Tell Us About Yourself/i)
    end
  end

  describe "on GMAT" do
    it "shows 'Not found Page'" do
      visit greprepclub_plans_path
      expect(page).to have_content("not found")
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit greprepclub_plans_path
      expect(page).to have_content("not found")
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "shows 'Not found Page'" do
      visit greprepclub_plans_path
      expect(page).to have_content("not found")
    end
  end
end
