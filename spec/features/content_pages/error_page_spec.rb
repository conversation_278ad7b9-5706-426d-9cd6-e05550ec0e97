require "spec_helper"

RSpec.feature "Error page" do
  let!(:user) { FactoryBot.create(:member) }

  around :each do |example|
    Rails.application.config.consider_all_requests_local = false
    example.run
    Rails.application.config.consider_all_requests_local = true
  end

  context "without login" do
    it "should respond with 500 page" do
      visit "/500"
      expect(page).to have_content("Oops, an error occurred")

      expect(page).to have_content("Score Guarantee")
      expect(page).to have_content("FOLLOW US")
    end
  end

  context "with a given member session" do
    it "should respond with 500 page" do
      login(user)
      visit "/500"
      expect(page).to have_content("Oops, an error occurred")

      click_link "Toolbox"

      expect(page).to have_content("Error Tracker")
      expect(page).not_to have_content("FOLLOW US")
    end
  end
end
