require "spec_helper"

RSpec.feature "User visits plans page", js: true do
  let(:default_sale) { create(:sale_banner, start_date: Date.parse("2022-12-07"), end_date: Date.parse("2022-12-13"), sale_type: "default") }

  before do
    allow(SaleBanner).to receive(:active_sale).and_return(default_sale)
    visit plans_path
  end

  it "is reachable from homepage" do
    visit_homepage

    within("nav") do
      click_link "TRY NOW"
    end

    expect(page).to have_content "Get started on the road to test-day success with one of our GMAT study plans."
    expect(page).to have_current_path(plans_path)
  end

  it "renders properly" do
    expect(page).to have_content "Get started on the road to test-day success with one of our GMAT study plans."
    expect(page).to have_current_path(plans_path)
  end

  it "opens subscription modal from Advertisement link" do
    click_link "Try our course for free"

    expect(page).to have_content(/CONTACT INFORMATION/i)
  end

  it "opens subscription modal from 'start now' link" do
    click_link "start now"
    expect(page).to have_content(/Just one more step/i)
  end

  context "when user purchases trial plan" do
    before do
      click_link "start now"
    end

    context "when free trial is enabled" do
      before :each do
        stub_const("EXAM_NAME", "sat")

        Setting.create(key: "free_trial_enabled", value: true)
      end

      it "registers student information" do
        visit plans_path

        click_link "start now"
        user = FactoryBot.build(:user)

        within("#trial-user-subscription") do
          fill_in "user[first_name]", with: user.first_name
          fill_in "user[last_name]", with: user.last_name
          fill_in "user[email]", with: user.email
          fill_in "user[email_confirmation]", with: user.email

          find("[id='terms']").click
          find("[type='submit']").click
        end

        expect(page).to have_content "A confirmation email has been sent to your account"
        expect(User.count).to eq 1

        click_link "click here to resend"
        expect(page).to have_content "Email already sent. You can try again in 5 minutes."
      end
    end

    context "when free trial is disabled" do
      let(:user) { FactoryBot.build(:lead) }

      it "registers student information" do
        allow_any_instance_of(LeadsController).to receive(:verify_recaptcha_or_respond_with_error).and_return(true)

        within("#lead-subscription") do
          fill_in "lead[first_name]", with: user.first_name
          fill_in "lead[last_name]", with: user.last_name
          fill_in "lead[email]", with: user.email
          fill_in "lead[email_confirmation]", with: user.email

          find("[type='submit']").click
        end

        expect(page).to have_css("body", text: "BILLING INFORMATION", visible: false)
        expect(Lead.count).to eq 1
      end
    end
  end

  context "when user visit gre plan page" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "redirects to plans page" do
      visit plans_path

      expect(page).to have_content(/Self-Study Courses/)
      expect(page).to have_content(/Live Online Classes/)
      expect(page).to have_current_path(plans_path)
    end
  end

  context "when user login and visit plan page" do
    let!(:user) { create(:member, :with_billing_info) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      add_stub_to_user("current_subscription", generate_recurly_subscription)
      add_stub_to_user("current_subscription.plan", generate_recurly_plan)

      visit new_subscription_path
    end

    it "redirects to plans page" do
      visit plans_path

      expect(page).to have_content(/ACTIVE/)
      expect(page).to have_current_path(plans_path)
    end
  end

  context "When ondemand is enabled and gmat exam" do
    let!(:user) { create(:member, :with_billing_info) }

    before :each do
      Setting.create(key: "ondemand_enabled", value: true)
      Setting.create(key: "free_trial_enabled", value: true)
      stub_const("EXAM_NAME", "gmat")
      visit plans_path
    end

    it "visit ondemand plans page" do
      expect(page).to have_content("5-day full course access for FREE!")
      expect(page).to have_content("Live Online Classes")
      expect(page).to have_content("Interested in a custom tutoring package?")
    end

    it "visit ondemand plans page and access online classes" do
      expect(page).to have_content("5 SPOTS LEFT!")
      expect(page).to have_content("1,599")
      expect(page).to have_content("TTP OnDemand")
      expect(page).to have_content("Live Online Classes")
    end

    it "navigates to the LiveTeach classes page from the OnDemand plans page" do
      find(".get-started-btn .plan-g").click

      expect(page).to have_content("LiveTeach")
      expect(page).to have_content("Class Schedules")
      expect(page).to have_current_path(online_classes_path)
    end
  end

  context "when free trial account user login and visit plan page" do
    let!(:user) { create(:member, on_trial: true) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      add_stub_to_user("current_subscription", generate_recurly_subscription)
      add_stub_to_user("current_subscription.plan", generate_recurly_plan)

      Setting.create(key: "free_trial_enabled", value: true)

      visit new_subscription_path
    end

    it "redirects to the plans page, clicks the 'start now' button, and a red toast message appears on the screen." do
      visit plans_path

      find("#trial-sign-up a").click

      expect(page).to have_content(/You've already logged into a Target Test Prep Self-Study account./)
      expect(page).to have_current_path(plans_path)
    end
  end

  context "when free trial account expired user login and visit partner page" do
    let!(:user) { create(:member, on_trial: false, current_plan: "5daystrial") }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      add_stub_to_user("current_subscription", generate_recurly_subscription)
      add_stub_to_user("current_subscription.plan", generate_recurly_plan)

      Setting.create(key: "free_trial_enabled", value: true)
      allow_any_instance_of(User).to receive(:valid_membership?).and_return(false)

      visit new_subscription_path
    end

    it "redirects to the partner page" do
      visit gmatclub_plans_path

      within ".trial-section" do
        click_link "TRY OUR COURSE FOR FREE"
      end

      expect(page).to have_content(/You have used your one trial. Please purchase a plan to continue./)
      expect(page).to have_current_path(gmatclub_plans_path)
    end
  end

  context "when free account user login and visit partner page" do
    let!(:user) { FactoryBot.create(:user, signed_up_for_free_account: true) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      allow_any_instance_of(User).to receive(:valid_membership?).and_return(false)

      visit new_subscription_path
    end

    it "redirects to the partner page" do
      visit gmatclub_plans_path

      within ".trial-section" do
        click_link "TRY OUR COURSE FOR FREE"
      end

      expect(page).to have_current_path(gmatclub_plans_path)
    end
  end

  context "when free account expired user login and visit plan page" do
    let!(:user) { FactoryBot.create(:user, signed_up_for_free_account: true) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      visit new_subscription_path
    end

    it "visit plan page and redirects to the new subscription page" do
      visit plans_path

      expect(page).to have_current_path(new_subscription_path)
    end
  end

  context "when free account user login and visit plan page" do
    let!(:user) { FactoryBot.create(:user, signed_up_for_free_account: true) }
    let!(:study_plan) { create(:study_plan, :complete, track: user.track) }

    before :each do
      stub_const("EXAM_NAME", "gmat")
      login(user)

      Setting.create(key: "free_trial_enabled", value: false)

      visit new_subscription_path
    end

    it "visit plan page and redirects to the new subscription page" do
      visit plans_path

      expect(page).to have_current_path(new_subscription_path)
    end
  end
end
