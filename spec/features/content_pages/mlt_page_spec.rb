require "spec_helper"

RSpec.feature "User visits MLT page" do
  describe "on GMAT" do
    it "renders properly" do
      visit mlt_path

      expect(page).to have_content "MLT-Focused"
      expect(page).to have_current_path(mlt_path)
    end

    context "when click on enroll now" do
      it "redirects to subscribe page" do
        visit mlt_path

        expect(page).to have_link "enroll now"

        first(".hero").find(:link, "enroll now").click

        expect(page).to have_content("1-year access MLT")
      end
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders properly" do
      visit mlt_path

      expect(page).to have_content("MLT-FOCUSED")
      expect(page).to have_selector(".girl-using-ipad")
      expect(page).to have_current_path(mlt_path)
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit mlt_path

      expect(page).to have_content("MLT-FOCUSED")
      expect(page).to have_current_path(mlt_path)
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "shows 'Not found Page'" do
      visit mlt_path
      expect(page).to have_content("not found")
    end
  end
end
