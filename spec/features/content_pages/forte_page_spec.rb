require "spec_helper"

RSpec.feature "User visits Forte page" do
  describe "on GMAT" do
    it "renders properly" do
      visit forte_path

      expect(page).to have_content "FORTÉ-Focused"
      expect(page).to have_current_path(forte_path)
    end

    context "when click on enroll now" do
      it "redirects to subscribe page" do
        visit forte_path

        expect(page).to have_link "enroll now"

        first(".hero").find(:link, "enroll now").click

        expect(page).to have_content("1-year access Forte")
      end
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders properly" do
      visit forte_path

      expect(page).to have_content "FORTÉ-FOCUSED"
      expect(page).to have_selector(".partner-introduction")
      expect(page).to have_current_path(forte_path)
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "renders properly" do
      visit forte_path

      expect(page).to have_content "FORTÉ-FOCUSED"
      expect(page).to have_current_path(forte_path)
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "shows 'Not found Page'" do
      visit forte_path
      expect(page).to have_content("not found")
    end
  end
end
