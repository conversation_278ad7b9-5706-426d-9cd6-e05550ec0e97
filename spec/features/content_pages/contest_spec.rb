require "spec_helper"

RSpec.feature "User visits contest page" do
  describe "on GMAT" do
    it "shows 'Not found Page'" do
      visit "/giveaway"

      expect(page).to have_content "Enter to Win"
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders properly" do
      visit "/giveaway"

      expect(page).to have_content "Enter to Win"
    end
  end
end
