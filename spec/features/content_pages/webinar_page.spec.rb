require "spec_helper"

RSpec.feature "User visits Webinar page" do
  describe "on GMAT" do
    it "renders properly" do
      visit webinar_path

      expect(page).to have_content "Webinar Series"
      expect(page).to have_current_path(webinar_path)
    end
  end

  describe "on GRE" do
    before :each do
      stub_const("EXAM_NAME", "gre")
    end

    it "renders properly" do
      visit webinar_path

      expect(page).to have_content "Webinar Series"
      expect(page).to have_selector(".advertising")
      expect(page).to have_current_path(webinar_path)
    end
  end

  describe "on EA" do
    before :each do
      stub_const("EXAM_NAME", "ea")
    end

    it "shows 'Not found Page'" do
      visit webinar_path
      expect(page).to have_content("not found")
    end
  end

  describe "on SAT" do
    before :each do
      stub_const("EXAM_NAME", "sat")
    end

    it "shows 'Not found Page'" do
      visit webinar_path
      expect(page).to have_content("not found")
    end
  end
end
