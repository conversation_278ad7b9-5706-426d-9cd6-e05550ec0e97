require "spec_helper"

RSpec.feature "Not Found page" do
  let!(:user) { FactoryBot.create(:member) }

  around :each do |example|
    Rails.application.config.consider_all_requests_local = false
    example.run
    Rails.application.config.consider_all_requests_local = true
  end

  context "without login" do
    it "should respond with 404 page" do
      visit "/foo"
      expect(page).to have_content("not found")

      expect(page).to have_content("Score Guarantee")
      expect(page).to have_content("FOLLOW US")
    end
  end

  context "with a given member session" do
    it "should respond with 404 page" do
      login(user)
      visit "/foo"
      expect(page).to have_content("not found")

      click_link "Toolbox"

      expect(page).to have_content("Error Tracker")
      expect(page).not_to have_content("FOLLOW US")
    end
  end

  context "with a given member session accessing an admin url" do
    it "should respond with 404 page" do
      login(user)
      visit "/new_admin/dashboard"
      expect(page).to have_content("not found")

      click_link "Toolbox"

      expect(page).to have_content("Error Tracker")
      expect(page).not_to have_content("FOLLOW US")
    end
  end
end
