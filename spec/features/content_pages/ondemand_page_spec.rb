require "spec_helper"

RSpec.feature "User visits OnDemand page", js: true do
  context "when OnDemand is enabled and exam name is GMAT" do
    before do
      Setting.create(key: "ondemand_enabled", value: true)
      stub_const("EXAM_NAME", "gmat")
      visit "/ondemand"
    end

    it "renders properly" do
      expect(page).to have_content "The most comprehensive video course for the newest GMAT"
      expect(page).to have_current_path("/ondemand")
    end

    it "renders the video for ondemand section" do
      find(".what-is-ondemand-video").click

      expect(page).to have_selector("#video-player-modal", visible: true)
    end

    it "renders the video for section quant" do
      click_on "video-quant"

      expect(page).to have_selector("#video-player-modal", visible: true)
    end

    it "renders the video for section verbal" do
      click_on "video-verbal"

      expect(page).to have_selector("#video-player-modal", visible: true)
    end

    it "renders the video for section di" do
      click_on "video-di"

      expect(page).to have_selector("#video-player-modal", visible: true)
    end
  end

  context "when <PERSON><PERSON><PERSON><PERSON> is disabled" do
    before do
      Setting.create(key: "ondemand_enabled", value: false)
      stub_const("EXAM_NAME", "gmat")
    end

    it "renders 404" do
      visit "/ondemand"

      expect(page).to have_current_path("/ondemand")
      expect(page).to have_content "Page not found"
      expect(page).to_not have_content "The most comprehensive video course for the GMAT"
    end
  end
end
