require "spec_helper"

RSpec.feature "User visits privacy policy page" do
  it "is reachable from homepage" do
    visit_homepage

    within("footer") do
      click_link "Privacy Policy"
    end

    expect(page).to have_content "Privacy Policy"
    expect(page).to have_current_path(privacy_path)
  end

  it "renders properly" do
    visit privacy_path

    expect(page).to have_content "Privacy Policy"
    expect(page).to have_current_path(privacy_path)
    expect(page).to have_selector( "header#guest-navbar", count: 1)
    expect(page).to have_selector( "footer#guest-footer", count: 1)
  end
end
