require "spec_helper"

RSpec.feature "User visits terms and conditions page" do
  it "is reachable from homepage" do
    visit_homepage

    within("footer") do
      click_link "Terms and Conditions"
    end

    expect(page).to have_content "Terms and Conditions"
    expect(page).to have_current_path(terms_path)
  end

  it "renders properly" do
    visit terms_path

    expect(page).to have_content "Terms and Conditions"
    expect(page).to have_current_path(terms_path)
    expect(page).to have_selector( "header#guest-navbar", count: 1)
    expect(page).to have_selector( "footer#guest-footer", count: 1)
  end
end
